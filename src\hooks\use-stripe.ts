"use client";

import * as React from "react";
import { loadStripe } from '@stripe/stripe-js';
import { useAuth } from "./use-auth";
import { toast } from "./use-toast";

const stripePromise = loadStripe(
  process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!
);

export function useStripe() {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = React.useState(false);

  const createCheckoutSession = async () => {
    if (!user) {
      toast({
        variant: "destructive",
        title: "Authentication Required",
        description: "Please sign in to upgrade your subscription.",
      });
      return;
    }

    setIsLoading(true);

    try {
      // Create checkout session for one-time payment
      const response = await fetch('/api/stripe/create-checkout-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.id,
        }),
      });

      const { sessionId, error } = await response.json();

      if (error) {
        throw new Error(error);
      }

      // Redirect to Stripe Checkout
      const stripe = await stripePromise;
      if (!stripe) {
        throw new Error('Stripe failed to load');
      }

      const { error: stripeError } = await stripe.redirectToCheckout({
        sessionId,
      });

      if (stripeError) {
        throw new Error(stripeError.message);
      }
    } catch (error) {
      console.error('Error creating checkout session:', error);
      toast({
        variant: "destructive",
        title: "Payment Error",
        description: error instanceof Error ? error.message : "Failed to start checkout process.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const upgradeToPremium = () => {
    createCheckoutSession();
  };

  return {
    isLoading,
    upgradeToPremium,
    createCheckoutSession,
  };
}
