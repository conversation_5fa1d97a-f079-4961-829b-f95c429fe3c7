
"use client";

import * as React from 'react';
import { Check<PERSON>ir<PERSON>, Crown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useTransactions } from '@/hooks/use-transactions';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';
import { useStripe } from '@/hooks/use-stripe';

const freeFeatures = [
    "3 Wallets",
    "5 Categories",
    "2 Months of History",
];

const premiumFeatures = [
    "Unlimited Wallets",
    "Unlimited Categories",
    "Unlimited History",
    "Priority Support",
];

export default function SubscriptionPage() {
    const { subscription, setSubscriptionTier } = useTransactions();
    const { toast } = useToast();
    const { upgradeToPremium, isLoading } = useStripe();

    const handleUpgrade = () => {
        upgradeToPremium();
    };

    return (
        <div className="space-y-8">
             <div className="text-center">
                <h1 className="text-3xl font-bold">Choose Your Plan</h1>
                <p className="text-muted-foreground mt-2">Unlock more features and take full control of your finances.</p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
                <TierCard
                    title="Free"
                    price="$0"
                    period="forever"
                    description="Get started with the essentials for tracking your finances."
                    features={freeFeatures}
                    isActive={subscription.tier === 'free'}
                    onSelect={() => {}}
                    buttonText={subscription.tier === 'free' ? 'Current Plan' : 'Previous Plan'}
                    disabled={true}
                />
                <TierCard
                    title="Premium"
                    price="$5.00"
                    period="one-time"
                    description="Lifetime access to all premium features."
                    features={premiumFeatures}
                    isPremium
                    isActive={subscription.tier === 'premium'}
                    onSelect={handleUpgrade}
                    buttonText={subscription.tier === 'premium' ? 'Current Plan' : 'Buy Now'}
                    disabled={subscription.tier === 'premium'}
                    isLoading={isLoading}
                />
            </div>
        </div>
    );
}


interface TierCardProps {
    title: string;
    price: string;
    period: string;
    description: string;
    features: string[];
    isPremium?: boolean;
    isActive: boolean;
    onSelect: () => void;
    buttonText: string;
    disabled?: boolean;
    isLoading?: boolean;
}

function TierCard({
    title,
    price,
    period,
    description,
    features,
    isPremium = false,
    isActive,
    onSelect,
    buttonText,
    disabled = false,
    isLoading = false
}: TierCardProps) {
    return (
        <Card className={cn("bg-card-gradient flex flex-col", isActive && "border-primary ring-2 ring-primary")}>
            <CardHeader className="text-center">
                <CardTitle className="text-2xl flex items-center justify-center gap-2">
                    {isPremium && <Crown className="w-6 h-6 text-yellow-400" />}
                    {title}
                </CardTitle>
                <div className="mt-2">
                    <span className="text-3xl font-bold">{price}</span>
                    <span className="text-muted-foreground ml-1">/{period}</span>
                </div>
                <CardDescription>{description}</CardDescription>
            </CardHeader>
            <CardContent className="flex-1">
                <ul className="space-y-3">
                    {features.map((feature, index) => (
                        <li key={index} className="flex items-center gap-3">
                            <CheckCircle className="w-5 h-5 text-green-500" />
                            <span>{feature}</span>
                        </li>
                    ))}
                </ul>
            </CardContent>
            <CardFooter>
                <Button
                    className="w-full"
                    onClick={onSelect}
                    disabled={disabled || isLoading}
                    variant={isPremium ? 'default' : 'outline'}
                >
                    {isLoading ? (
                        <>
                            <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent" />
                            Processing...
                        </>
                    ) : (
                        buttonText
                    )}
                </Button>
            </CardFooter>
        </Card>
    );
}
