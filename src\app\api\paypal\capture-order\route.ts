import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

const PAYPAL_CLIENT_ID = process.env.PAYPAL_CLIENT_ID!;
const PAYPAL_CLIENT_SECRET = process.env.PAYPAL_CLIENT_SECRET!;
const PAYPAL_BASE_URL = process.env.NODE_ENV === 'production' 
  ? 'https://api-m.paypal.com' 
  : 'https://api-m.sandbox.paypal.com';

async function getPayPalAccessToken() {
  const auth = Buffer.from(`${PAYPAL_CLIENT_ID}:${PAYPAL_CLIENT_SECRET}`).toString('base64');
  
  const response = await fetch(`${PAYPAL_BASE_URL}/v1/oauth2/token`, {
    method: 'POST',
    headers: {
      'Authorization': `Basic ${auth}`,
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: 'grant_type=client_credentials',
  });

  const data = await response.json();
  return data.access_token;
}

async function updateUserSubscription(userId: string, tier: 'free' | 'premium') {
  try {
    const supabase = await createClient();
    
    // Get current user data
    const { data: profile, error: fetchError } = await supabase
      .from('profiles')
      .select('data')
      .eq('id', userId)
      .single();

    if (fetchError) {
      console.error('Error fetching user profile:', fetchError);
      return;
    }

    // Update subscription tier in the data
    const updatedData = {
      ...profile.data,
      subscription: { tier }
    };

    // Save updated data back to Supabase
    const { error: updateError } = await supabase
      .from('profiles')
      .update({ data: updatedData })
      .eq('id', userId);

    if (updateError) {
      console.error('Error updating user subscription:', updateError);
    } else {
      console.log(`Updated user ${userId} subscription to ${tier}`);
    }
  } catch (error) {
    console.error('Error in updateUserSubscription:', error);
  }
}

export async function POST(request: NextRequest) {
  try {
    const { orderId, userId } = await request.json();

    if (!orderId || !userId) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // Verify user is authenticated
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user || user.id !== userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get PayPal access token
    const accessToken = await getPayPalAccessToken();

    // Capture the PayPal order
    const captureResponse = await fetch(`${PAYPAL_BASE_URL}/v2/checkout/orders/${orderId}/capture`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    const captureData = await captureResponse.json();

    if (!captureResponse.ok) {
      console.error('PayPal order capture failed:', captureData);
      return NextResponse.json(
        { error: 'Failed to capture PayPal order' },
        { status: 500 }
      );
    }

    // Check if payment was successful
    const captureStatus = captureData.purchase_units?.[0]?.payments?.captures?.[0]?.status;
    
    if (captureStatus === 'COMPLETED') {
      // Update user's subscription status to premium
      await updateUserSubscription(userId, 'premium');
      console.log(`PayPal payment completed for user ${userId}, order ${orderId}`);
      
      return NextResponse.json({ success: true, captureData });
    } else {
      console.error('PayPal payment not completed:', captureData);
      return NextResponse.json(
        { error: 'Payment not completed' },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Error capturing PayPal order:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
