import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { createClient } from '@/utils/supabase/server';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-06-30.basil',
});

const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET!;

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const signature = request.headers.get('stripe-signature')!;

    let event: Stripe.Event;

    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
    } catch (err) {
      console.error('Webhook signature verification failed:', err);
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 400 }
      );
    }

    const supabase = createClient();

    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object as Stripe.Checkout.Session;
        const userId = session.metadata?.userId;

        if (userId && session.mode === 'payment' && session.payment_status === 'paid') {
          // Update user's subscription status to premium for one-time payment
          await updateUserSubscription(userId, 'premium');
          console.log(`One-time payment completed for user ${userId}`);
        }
        break;
      }

      case 'payment_intent.succeeded': {
        const paymentIntent = event.data.object as Stripe.PaymentIntent;
        const userId = paymentIntent.metadata?.userId;

        if (userId) {
          // Additional confirmation that payment was successful
          console.log(`Payment confirmed for user ${userId}`);
        }
        break;
      }

      case 'payment_intent.payment_failed': {
        const paymentIntent = event.data.object as Stripe.PaymentIntent;
        const userId = paymentIntent.metadata?.userId;

        if (userId) {
          console.log(`Payment failed for user ${userId}`);
          // Optionally notify user or take other action
        }
        break;
      }

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Webhook error:', error);
    return NextResponse.json(
      { error: 'Webhook handler failed' },
      { status: 500 }
    );
  }
}

async function updateUserSubscription(userId: string, tier: 'free' | 'premium') {
  try {
    const supabase = await createClient();
    
    // Get current user data
    const { data: profile, error: fetchError } = await supabase
      .from('profiles')
      .select('data')
      .eq('id', userId)
      .single();

    if (fetchError) {
      console.error('Error fetching user profile:', fetchError);
      return;
    }

    // Update subscription tier in the data
    const updatedData = {
      ...profile.data,
      subscription: { tier }
    };

    // Save updated data back to Supabase
    const { error: updateError } = await supabase
      .from('profiles')
      .update({ data: updatedData })
      .eq('id', userId);

    if (updateError) {
      console.error('Error updating user subscription:', updateError);
    } else {
      console.log(`Updated user ${userId} subscription to ${tier}`);
    }
  } catch (error) {
    console.error('Error in updateUserSubscription:', error);
  }
}
