# Stripe Integration Setup Guide

## Overview
ExpenseFlow now includes Stripe integration for premium one-time payment upgrades. This guide will help you set up Stripe for your application.

### Pricing Model
- **Free Tier**: Basic features with limitations
- **Premium Tier**: $49.99 one-time payment for lifetime access
- **No Subscriptions**: Simple one-time purchase, no recurring billing

## Prerequisites
- Stripe account (create at https://stripe.com)
- Supabase project with profiles table set up
- Next.js application running

## Step 1: Install Stripe Dependencies

```bash
npm install stripe @stripe/stripe-js
```

## Step 2: Stripe Dashboard Setup

### 2.1 Products (Optional)
For one-time payments, you don't need to create products in advance since we create them dynamically. However, you can optionally create a product for reference:

1. Go to your Stripe Dashboard
2. Navigate to **Products** → **Add Product**
3. Create a product named "ExpenseFlow Premium (Lifetime)"
4. Note: We don't need to create prices as they're generated dynamically

### 2.2 Get API Keys
1. Go to **Developers** → **API Keys**
2. Copy your **Publishable key** (starts with `pk_test_` or `pk_live_`)
3. Copy your **Secret key** (starts with `sk_test_` or `sk_live_`)

### 2.3 Set up Webhooks
1. Go to **Developers** → **Webhooks**
2. Click **Add endpoint**
3. Set endpoint URL: `https://yourdomain.com/api/stripe/webhook`
4. Select events to listen for (for one-time payments):
   - `checkout.session.completed`
   - `payment_intent.succeeded`
   - `payment_intent.payment_failed`
5. Copy the **Signing secret** (starts with `whsec_`)

## Step 3: Environment Variables

Update your `.env.local` file:

```env
# Stripe Configuration (One-time Payment)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key_here
STRIPE_SECRET_KEY=sk_test_your_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
# Note: For one-time payments, we don't need a price ID as we create the price dynamically
```

## Step 4: Test the Integration

### 4.1 Test Mode
- Use Stripe's test card numbers for testing
- Test card: `4242 4242 4242 4242`
- Any future expiry date and CVC

### 4.2 Test Flow
1. Sign in to your app
2. Go to subscription page
3. Click "Buy Now" on Premium plan ($49.99 one-time)
4. Complete checkout with test card
5. Verify premium status is activated immediately

## Step 5: Production Deployment

### 5.1 Switch to Live Mode
1. Toggle to **Live mode** in Stripe Dashboard
2. Update environment variables with live keys
3. Update webhook endpoint to production URL

### 5.2 Security Checklist
- ✅ Webhook endpoint is secured with signature verification
- ✅ API keys are stored as environment variables
- ✅ User authentication is verified before creating sessions
- ✅ Subscription status is validated server-side

## Features Implemented

### ✅ One-Time Payment Management
- Create checkout sessions for premium upgrades ($49.99 one-time)
- Handle payment completion events via webhooks
- Update user premium status in Supabase immediately
- Automatic profile creation with default free tier

### ✅ User Experience
- Loading states during checkout process
- Success page after payment completion
- Error handling with user-friendly messages
- Pricing display with lifetime access ($49.99 one-time)

### ✅ Security
- Server-side user authentication verification
- Webhook signature validation
- Row-level security in Supabase
- Secure API key management

## API Endpoints

### POST `/api/stripe/create-checkout-session`
Creates a Stripe checkout session for one-time premium upgrade.

**Request:**
```json
{
  "userId": "user-uuid"
}
```

**Response:**
```json
{
  "sessionId": "cs_xxx"
}
```

### POST `/api/stripe/webhook`
Handles Stripe webhook events for payment completion and status updates.

## Troubleshooting

### Common Issues

1. **Webhook not receiving events**
   - Check webhook URL is accessible
   - Verify webhook secret is correct
   - Check Stripe Dashboard logs

2. **Checkout session creation fails**
   - Verify API keys are correct
   - Check user authentication
   - Ensure Stripe keys are properly configured

3. **Premium status not updating**
   - Check webhook events are being received
   - Verify Supabase connection
   - Check user ID mapping in payment metadata

### Testing Webhooks Locally
Use Stripe CLI for local webhook testing:

```bash
stripe listen --forward-to localhost:3000/api/stripe/webhook
```

## Key Differences from Subscription Model

| Feature | Subscription Model | One-Time Payment Model ✅ |
|---------|-------------------|---------------------------|
| **Pricing** | $9.99/month | $49.99 one-time |
| **Billing** | Recurring | Single payment |
| **Setup** | Requires price IDs | Dynamic product creation |
| **Webhooks** | Subscription events | Payment completion events |
| **User Experience** | Ongoing billing | Lifetime access |
| **Revenue** | $119.88/year | $49.99 lifetime |

## Support
For issues with Stripe integration, check:
- Stripe Dashboard logs
- Browser console errors
- Server logs for webhook processing
- Supabase logs for database operations

## Quick Start Checklist
- [ ] Install Stripe dependencies (`npm install stripe @stripe/stripe-js`)
- [ ] Get Stripe API keys from dashboard
- [ ] Set up webhook endpoint with required events
- [ ] Update environment variables
- [ ] Test with Stripe test cards
- [ ] Deploy and configure production webhooks
