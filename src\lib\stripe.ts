import { loadStripe } from '@stripe/stripe-js';

// Initialize Stripe
export const stripePromise = loadStripe(
  process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!
);

// Stripe configuration for one-time payments
export const STRIPE_CONFIG = {
  currency: 'usd',
  premium: {
    amount: 4999, // $49.99 in cents
    name: 'ExpenseFlow Premium (Lifetime)',
    description: 'Lifetime access to all premium features',
    features: [
      'Unlimited Wallets',
      'Unlimited Categories',
      'Unlimited History',
      'Advanced Analytics',
      'AI Description Suggestions',
      'Priority Support'
    ]
  },
} as const;
